import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout, QLabel,
                             QPushButton, QLineEdit, QTableWidget, QTableWidgetItem, QHBoxLayout, QSpinBox)
import mysql.connector
import firebase_admin
from firebase_admin import credentials, db

# Database Configuration
MYSQL_CONFIG = {
    "host": "localhost",
    "user": "root",
    "password": "password",
    "database": "factory_db"
}

FIREBASE_CONFIG = "data/factory-trade-80479-firebase-adminsdk-fbsvc-6997f7d8b9.json"

# Initialize Firebase
cred = credentials.Certificate(FIREBASE_CONFIG)
firebase_admin.initialize_app(cred, {
    "databaseURL": "https://factory-trade-80479-default-rtdb.europe-west1.firebasedatabase.app/"
})

class FactoryManagementApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Factory & Store Management")
        self.setGeometry(100, 100, 1200, 800)
        
        self.tabs = QTabWidget()
        self.setCentralWidget(self.tabs)
        
        self.init_ui()
    
    def init_ui(self):
        self.tabs.addTab(self.create_inventory_tab(), "Inventory")
        self.tabs.addTab(self.create_sales_tab(), "Sales")
        self.tabs.addTab(self.create_orders_tab(), "Orders")
        self.tabs.addTab(self.create_customers_tab(), "Customers")
        self.tabs.addTab(self.create_reports_tab(), "Reports")
    
    def create_inventory_tab(self):
        tab = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel("Inventory Management"))
        
        self.inventory_table = QTableWidget()
        self.inventory_table.setColumnCount(5)
        self.inventory_table.setHorizontalHeaderLabels(["SKU", "Product Name", "Stock Level", "Production Price", "Consumer Price"])
        layout.addWidget(self.inventory_table)
        
        form_layout = QHBoxLayout()
        self.sku_input = QLineEdit()
        self.sku_input.setPlaceholderText("Enter SKU or Scan Barcode")
        self.product_input = QLineEdit()
        self.product_input.setPlaceholderText("Enter Product Name")
        self.stock_input = QLineEdit()
        self.stock_input.setPlaceholderText("Enter Stock Level")
        self.prod_price_input = QLineEdit()
        self.prod_price_input.setPlaceholderText("Enter Production Price")
        self.cons_price_input = QLineEdit()
        self.cons_price_input.setPlaceholderText("Enter Consumer Price")
        add_button = QPushButton("Add Item")
        add_button.clicked.connect(self.add_inventory_item)
        
        form_layout.addWidget(self.sku_input)
        form_layout.addWidget(self.product_input)
        form_layout.addWidget(self.stock_input)
        form_layout.addWidget(self.prod_price_input)
        form_layout.addWidget(self.cons_price_input)
        form_layout.addWidget(add_button)
        layout.addLayout(form_layout)
        
        tab.setLayout(layout)
        return tab
    
    def add_inventory_item(self):
        sku = self.sku_input.text()
        product_name = self.product_input.text()
        stock_level = self.stock_input.text()
        prod_price = self.prod_price_input.text()
        cons_price = self.cons_price_input.text()
        
        if sku and product_name and stock_level and prod_price and cons_price:
            row_position = self.inventory_table.rowCount()
            self.inventory_table.insertRow(row_position)
            self.inventory_table.setItem(row_position, 0, QTableWidgetItem(sku))
            self.inventory_table.setItem(row_position, 1, QTableWidgetItem(product_name))
            self.inventory_table.setItem(row_position, 2, QTableWidgetItem(stock_level))
            self.inventory_table.setItem(row_position, 3, QTableWidgetItem(prod_price))
            self.inventory_table.setItem(row_position, 4, QTableWidgetItem(cons_price))
    
    def create_sales_tab(self):
        tab = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel("Sales & Invoicing"))
        
        self.sales_input = QLineEdit()
        self.sales_input.setPlaceholderText("Scan Barcode or Enter SKU or Product Name")
        self.sales_input.textChanged.connect(self.filter_inventory)
        layout.addWidget(self.sales_input)
        
        self.sales_table = QTableWidget()
        self.sales_table.setColumnCount(4)
        self.sales_table.setHorizontalHeaderLabels(["SKU", "Product Name", "Quantity", "Price"])
        layout.addWidget(self.sales_table)
        
        self.quantity_input = QSpinBox()
        self.quantity_input.setMinimum(1)
        layout.addWidget(self.quantity_input)
        
        self.total_price_label = QLabel("Total: $0.00")
        layout.addWidget(self.total_price_label)
        
        pay_button = QPushButton("Pay")
        pay_button.clicked.connect(self.process_payment)
        layout.addWidget(pay_button)
        
        self.sales_input.returnPressed.connect(self.add_to_invoice)
        
        tab.setLayout(layout)
        return tab
    
    def create_orders_tab(self):
        tab = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel("Orders Management"))
        tab.setLayout(layout)
        return tab
    
    def create_customers_tab(self):
        tab = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel("Customer Transactions"))
        tab.setLayout(layout)
        return tab
    
    def create_reports_tab(self):
        tab = QWidget()
        layout = QVBoxLayout()
        layout.addWidget(QLabel("Financial Reports & Analytics"))
        tab.setLayout(layout)
        return tab

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = FactoryManagementApp()
    window.show()
    sys.exit(app.exec())
